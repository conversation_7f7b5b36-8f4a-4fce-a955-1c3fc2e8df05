import React from 'react';
import styled from 'styled-components';
import { TimeWalletProvider } from './context/TimeWalletContext';
import WalletDisplay from './components/WalletDisplay';
import FocusControls from './components/FocusControls';
import Statistics from './components/Statistics';
import Header from './components/Header';

const AppContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  color: var(--text-primary);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const MainContent = styled.main`
  width: 100%;
  max-width: 1200px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
  margin-top: 20px;

  @media (min-width: 768px) {
    grid-template-columns: 2fr 1fr;
    gap: 40px;
  }
`;

const LeftColumn = styled.div`
  display: flex;
  flex-direction: column;
  gap: 30px;
`;

const RightColumn = styled.div`
  display: flex;
  flex-direction: column;
  gap: 30px;
`;

function App() {
  return (
    <TimeWalletProvider>
      <AppContainer>
        <Header />
        <MainContent>
          <LeftColumn>
            <WalletDisplay />
            <FocusControls />
          </LeftColumn>
          <RightColumn>
            <Statistics />
          </RightColumn>
        </MainContent>
      </AppContainer>
    </TimeWalletProvider>
  );
}

export default App;
