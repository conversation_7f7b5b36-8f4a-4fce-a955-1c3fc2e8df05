import React from 'react';
import styled from 'styled-components';
import { TimeWalletProvider } from './context/TimeWalletContext';
import WalletDisplay from './components/WalletDisplay';
import FocusControls from './components/FocusControls';
import Statistics from './components/Statistics';
import Header from './components/Header';

const AppContainer = styled.div`
  min-height: 100vh;
  color: var(--text-primary);
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
`;

const HeroSection = styled.section`
  width: 100%;
  padding: 80px 20px 60px;
  text-align: center;
  position: relative;

  @media (min-width: 768px) {
    padding: 120px 20px 80px;
  }
`;

const HeroTitle = styled.h1`
  font-size: clamp(48px, 8vw, 72px);
  font-weight: 600;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--text-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 20px 0;
  letter-spacing: -0.025em;
  line-height: 1.1;
`;

const HeroSubtitle = styled.p`
  font-size: clamp(16px, 2.5vw, 20px);
  color: var(--text-tertiary);
  margin: 0 0 40px 0;
  max-width: 560px;
  margin-left: auto;
  margin-right: auto;
  font-weight: 400;
  line-height: 1.6;
`;

const MainContent = styled.main`
  width: 100%;
  max-width: 1400px;
  padding: 0 20px 80px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 40px;

  @media (min-width: 1024px) {
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    padding: 0 40px 80px;
  }
`;

const LeftColumn = styled.div`
  display: flex;
  flex-direction: column;
  gap: 40px;
`;

const RightColumn = styled.div`
  display: flex;
  flex-direction: column;
  gap: 40px;
`;

function App() {
  return (
    <TimeWalletProvider>
      <AppContainer>
        <Header />
        <HeroSection>
          <HeroTitle>TimeWallet</HeroTitle>
          <HeroSubtitle>
            Transform your time into virtual currency. Every moment counts, every second has value.
          </HeroSubtitle>
        </HeroSection>
        <MainContent>
          <LeftColumn>
            <WalletDisplay />
            <FocusControls />
          </LeftColumn>
          <RightColumn>
            <Statistics />
          </RightColumn>
        </MainContent>
      </AppContainer>
    </TimeWalletProvider>
  );
}

export default App;
