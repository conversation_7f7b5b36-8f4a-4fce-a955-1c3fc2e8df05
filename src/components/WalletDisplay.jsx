import React, { useState, useEffect, useRef } from 'react';
import styled, { keyframes } from 'styled-components';
import { useTimeWallet, SESSION_TYPES } from '../context/TimeWalletContext';

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const WalletContainer = styled.div`
  background-color: var(--bg-secondary);
  border-radius: 16px;
  padding: 30px;
  box-shadow: var(--shadow);
  animation: ${fadeIn} 0.5s ease-out;
  border: 1px solid var(--border-color);
`;

const WalletHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const WalletTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
`;

const SessionBadge = styled.div`
  background-color: ${props => {
    switch(props.sessionType) {
      case SESSION_TYPES.FOCUS:
        return 'rgba(0, 255, 136, 0.15)';
      case SESSION_TYPES.SHORT_BREAK:
        return 'rgba(255, 165, 2, 0.15)';
      case SESSION_TYPES.LONG_BREAK:
        return 'rgba(55, 66, 250, 0.15)';
      default:
        return 'var(--bg-tertiary)';
    }
  }};
  color: ${props => {
    switch(props.sessionType) {
      case SESSION_TYPES.FOCUS:
        return 'var(--accent-green)';
      case SESSION_TYPES.SHORT_BREAK:
        return 'var(--accent-orange)';
      case SESSION_TYPES.LONG_BREAK:
        return 'var(--accent-blue)';
      default:
        return 'var(--text-secondary)';
    }
  }};
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
`;

const BalanceDisplay = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 30px 0;
`;

const BalanceAmount = styled.div`
  font-size: 64px;
  font-weight: 700;
  color: var(--text-primary);
  font-variant-numeric: tabular-nums;
  letter-spacing: -1px;
  position: relative;
  display: flex;
  align-items: center;
  
  &::before {
    content: '$';
    font-size: 32px;
    position: relative;
    top: -8px;
    margin-right: 4px;
    opacity: 0.8;
  }
`;

const BalanceChange = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 16px;
  color: ${props => props.isPositive ? 'var(--accent-green)' : 'var(--accent-red)'};
  margin-top: 8px;
  font-weight: 500;
`;

const ChangeArrow = styled.span`
  font-size: 18px;
`;

const BalanceInfo = styled.div`
  color: var(--text-secondary);
  font-size: 14px;
  text-align: center;
  margin-top: 8px;
`;

const ProgressBarContainer = styled.div`
  width: 100%;
  height: 8px;
  background-color: var(--bg-tertiary);
  border-radius: 4px;
  margin-top: 30px;
  overflow: hidden;
`;

const ProgressBarFill = styled.div`
  height: 100%;
  width: ${props => props.percentage}%;
  background: linear-gradient(90deg, var(--accent-green), var(--accent-blue));
  border-radius: 4px;
  transition: width 0.5s ease-out;
`;

function WalletDisplay() {
  const { 
    balance, 
    currentSession, 
    constants, 
    isWithinDailyHours 
  } = useTimeWallet();
  
  const [displayBalance, setDisplayBalance] = useState(balance);
  const [balanceChange, setBalanceChange] = useState(0);
  const previousBalance = useRef(balance);

  // Update display balance with animation
  useEffect(() => {
    if (balance !== previousBalance.current) {
      setBalanceChange(balance - previousBalance.current);
      
      // Animate the balance change
      const step = (balance - displayBalance) / 10;
      const interval = setInterval(() => {
        setDisplayBalance(prev => {
          const next = prev + step;
          if ((step > 0 && next >= balance) || (step < 0 && next <= balance)) {
            clearInterval(interval);
            return balance;
          }
          return next;
        });
      }, 50);
      
      previousBalance.current = balance;
      
      return () => clearInterval(interval);
    }
  }, [balance]);

  // Format the balance for display
  const formatBalance = (value) => {
    return parseFloat(value).toFixed(2);
  };

  // Calculate percentage of balance remaining
  const percentageRemaining = (balance / constants.STARTING_BALANCE) * 100;

  // Get session display name
  const getSessionName = () => {
    switch(currentSession) {
      case SESSION_TYPES.FOCUS:
        return 'Focus';
      case SESSION_TYPES.SHORT_BREAK:
        return 'Short Break';
      case SESSION_TYPES.LONG_BREAK:
        return 'Long Break';
      default:
        return 'Idle';
    }
  };

  // Get session icon
  const getSessionIcon = () => {
    switch(currentSession) {
      case SESSION_TYPES.FOCUS:
        return '🧠';
      case SESSION_TYPES.SHORT_BREAK:
        return '☕';
      case SESSION_TYPES.LONG_BREAK:
        return '☕';
      default:
        return '⏱️';
    }
  };

  return (
    <WalletContainer>
      <WalletHeader>
        <WalletTitle>Your Time Wallet</WalletTitle>
        {currentSession !== SESSION_TYPES.IDLE && (
          <SessionBadge sessionType={currentSession}>
            {getSessionIcon()} {getSessionName()}
          </SessionBadge>
        )}
      </WalletHeader>
      
      <BalanceDisplay>
        <BalanceAmount>
          {formatBalance(displayBalance)}
        </BalanceAmount>
        
        {balanceChange !== 0 && (
          <BalanceChange isPositive={balanceChange > 0}>
            <ChangeArrow>{balanceChange > 0 ? '↑' : '↓'}</ChangeArrow>
            ${Math.abs(balanceChange).toFixed(2)}
          </BalanceChange>
        )}
        
        <BalanceInfo>
          {isWithinDailyHours 
            ? 'Your time is being converted to money in real-time'
            : 'Market is closed. Balance will reset at opening.'}
        </BalanceInfo>
      </BalanceDisplay>
      
      <ProgressBarContainer>
        <ProgressBarFill percentage={percentageRemaining} />
      </ProgressBarContainer>
    </WalletContainer>
  );
}

export default WalletDisplay;
