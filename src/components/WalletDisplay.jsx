import React, { useState, useEffect, useRef } from 'react';
import styled, { keyframes } from 'styled-components';
import { useTimeWallet, SESSION_TYPES } from '../context/TimeWalletContext';

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const WalletContainer = styled.div`
  background: var(--bg-secondary);
  border-radius: 16px;
  padding: 32px;
  box-shadow: var(--shadow-lg);
  animation: ${fadeIn} 0.6s ease-out;
  border: 1px solid var(--border-primary);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--border-secondary), transparent);
  }
`;

const WalletHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const WalletTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
`;

const SessionBadge = styled.div`
  background-color: ${props => {
    switch(props.sessionType) {
      case SESSION_TYPES.FOCUS:
        return 'rgba(16, 185, 129, 0.1)';
      case SESSION_TYPES.SHORT_BREAK:
        return 'rgba(245, 158, 11, 0.1)';
      case SESSION_TYPES.LONG_BREAK:
        return 'rgba(59, 130, 246, 0.1)';
      default:
        return 'var(--bg-tertiary)';
    }
  }};
  color: ${props => {
    switch(props.sessionType) {
      case SESSION_TYPES.FOCUS:
        return 'var(--accent-success)';
      case SESSION_TYPES.SHORT_BREAK:
        return 'var(--accent-warning)';
      case SESSION_TYPES.LONG_BREAK:
        return 'var(--accent-info)';
      default:
        return 'var(--text-secondary)';
    }
  }};
  border: 1px solid ${props => {
    switch(props.sessionType) {
      case SESSION_TYPES.FOCUS:
        return 'rgba(16, 185, 129, 0.2)';
      case SESSION_TYPES.SHORT_BREAK:
        return 'rgba(245, 158, 11, 0.2)';
      case SESSION_TYPES.LONG_BREAK:
        return 'rgba(59, 130, 246, 0.2)';
      default:
        return 'var(--border-primary)';
    }
  }};
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 13px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
`;

const BalanceDisplay = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 30px 0;
`;

const BalanceAmount = styled.div`
  font-size: clamp(48px, 8vw, 72px);
  font-weight: 600;
  color: var(--text-primary);
  font-variant-numeric: tabular-nums;
  letter-spacing: -0.025em;
  position: relative;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;

  &::before {
    content: '$';
    font-size: clamp(24px, 4vw, 36px);
    position: relative;
    top: -4px;
    margin-right: 8px;
    color: var(--text-tertiary);
    font-weight: 500;
  }
`;

const BalanceChange = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 16px;
  color: ${props => props.isPositive ? 'var(--accent-green)' : 'var(--accent-red)'};
  margin-top: 8px;
  font-weight: 500;
`;

const ChangeArrow = styled.span`
  font-size: 18px;
`;

const BalanceInfo = styled.div`
  color: var(--text-secondary);
  font-size: 14px;
  text-align: center;
  margin-top: 8px;
`;

const ProgressBarContainer = styled.div`
  width: 100%;
  height: 8px;
  background-color: var(--bg-tertiary);
  border-radius: 4px;
  margin-top: 30px;
  overflow: hidden;
`;

const ProgressBarFill = styled.div`
  height: 100%;
  width: ${props => props.percentage}%;
  background: linear-gradient(90deg, var(--accent-green), var(--accent-blue));
  border-radius: 4px;
  transition: width 0.5s ease-out;
`;

function WalletDisplay() {
  const {
    balance,
    currentSession,
    constants,
    isWithinDailyHours,
    lastBalanceChange
  } = useTimeWallet();

  const [displayBalance, setDisplayBalance] = useState(balance);
  const [balanceChange, setBalanceChange] = useState(0);
  const [showChange, setShowChange] = useState(false);
  const previousBalance = useRef(balance);

  // Update display balance with animation
  useEffect(() => {
    if (balance !== previousBalance.current) {
      const change = balance - previousBalance.current;
      setBalanceChange(Math.abs(change));
      setShowChange(true);

      // Animate the balance change
      const step = change / 10;
      const interval = setInterval(() => {
        setDisplayBalance(prev => {
          const next = prev + step;
          if ((step > 0 && next >= balance) || (step < 0 && next <= balance)) {
            clearInterval(interval);
            return balance;
          }
          return next;
        });
      }, 50);

      previousBalance.current = balance;

      // Hide change indicator after 3 seconds
      setTimeout(() => setShowChange(false), 3000);

      return () => clearInterval(interval);
    }
  }, [balance]);

  // Show real-time balance change based on lastBalanceChange
  useEffect(() => {
    if (lastBalanceChange > 0) {
      setBalanceChange(lastBalanceChange);
      setShowChange(true);
      setTimeout(() => setShowChange(false), 2000);
    }
  }, [lastBalanceChange]);

  // Format the balance for display
  const formatBalance = (value) => {
    return parseFloat(value).toFixed(2);
  };

  // Calculate percentage of balance remaining
  const percentageRemaining = (balance / constants.STARTING_BALANCE) * 100;

  // Get session display name
  const getSessionName = () => {
    switch(currentSession) {
      case SESSION_TYPES.FOCUS:
        return 'Focus Session';
      case SESSION_TYPES.SHORT_BREAK:
        return 'Short Break';
      case SESSION_TYPES.LONG_BREAK:
        return 'Long Break';
      default:
        return 'Idle';
    }
  };

  return (
    <WalletContainer>
      <WalletHeader>
        <WalletTitle>Your Time Wallet</WalletTitle>
        {currentSession !== SESSION_TYPES.IDLE && (
          <SessionBadge sessionType={currentSession}>
            {getSessionName()}
          </SessionBadge>
        )}
      </WalletHeader>
      
      <BalanceDisplay>
        <BalanceAmount>
          {formatBalance(displayBalance)}
        </BalanceAmount>
        
        {showChange && balanceChange > 0 && (
          <BalanceChange isPositive={false}>
            <ChangeArrow>↓</ChangeArrow>
            ${balanceChange.toFixed(2)}
          </BalanceChange>
        )}
        
        <BalanceInfo>
          {isWithinDailyHours 
            ? 'Your time is being converted to money in real-time'
            : 'Market is closed. Balance will reset at opening.'}
        </BalanceInfo>
      </BalanceDisplay>
      
      <ProgressBarContainer>
        <ProgressBarFill percentage={percentageRemaining} />
      </ProgressBarContainer>
    </WalletContainer>
  );
}

export default WalletDisplay;
