import React, { useState, useEffect } from 'react';
import styled, { keyframes } from 'styled-components';
import { useTimeWallet, SESSION_TYPES } from '../context/TimeWalletContext';

const pulse = keyframes`
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 255, 136, 0.4);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(0, 255, 136, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 255, 136, 0);
  }
`;

const ControlsContainer = styled.div`
  background-color: var(--bg-secondary);
  border-radius: 16px;
  padding: 30px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
`;

const ControlsTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 20px 0;
`;

const ButtonGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
  margin-bottom: 30px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 12px;
  }
`;

const SessionButton = styled.button`
  background-color: ${props => {
    if (props.isActive) {
      switch(props.sessionType) {
        case SESSION_TYPES.FOCUS:
          return 'var(--accent-green)';
        case SESSION_TYPES.SHORT_BREAK:
          return 'var(--accent-orange)';
        case SESSION_TYPES.LONG_BREAK:
          return 'var(--accent-blue)';
        default:
          return 'var(--bg-tertiary)';
      }
    }
    return 'var(--bg-tertiary)';
  }};
  color: ${props => props.isActive ? '#000' : 'var(--text-primary)'};
  border: 2px solid ${props => {
    if (props.isActive) return 'transparent';
    switch(props.sessionType) {
      case SESSION_TYPES.FOCUS:
        return 'var(--accent-green)';
      case SESSION_TYPES.SHORT_BREAK:
        return 'var(--accent-orange)';
      case SESSION_TYPES.LONG_BREAK:
        return 'var(--accent-blue)';
      default:
        return 'var(--border-color)';
    }
  }};
  border-radius: 12px;
  padding: 16px 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  min-height: 80px;
  animation: ${props => props.isActive && props.sessionType === SESSION_TYPES.FOCUS ? pulse : 'none'} 2s infinite;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const ButtonIcon = styled.div`
  font-size: 24px;
`;

const ButtonLabel = styled.div`
  font-size: 12px;
  text-align: center;
  line-height: 1.2;
`;

const SessionTimer = styled.div`
  background-color: var(--bg-tertiary);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  margin-bottom: 20px;
`;

const TimerDisplay = styled.div`
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  font-variant-numeric: tabular-nums;
  margin-bottom: 8px;
`;

const TimerLabel = styled.div`
  font-size: 14px;
  color: var(--text-secondary);
`;

const ActionButton = styled.button`
  width: 100%;
  background-color: ${props => props.variant === 'stop' ? 'var(--accent-red)' : 'var(--accent-green)'};
  color: #000;
  border: none;
  border-radius: 12px;
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

function FocusControls() {
  const { 
    currentSession, 
    sessionStartTime, 
    isActive, 
    startSession, 
    endSession,
    isWithinDailyHours 
  } = useTimeWallet();
  
  const [sessionTime, setSessionTime] = useState(0);

  // Update session timer
  useEffect(() => {
    if (isActive && sessionStartTime) {
      const interval = setInterval(() => {
        setSessionTime(Math.floor((Date.now() - sessionStartTime) / 1000));
      }, 1000);

      return () => clearInterval(interval);
    } else {
      setSessionTime(0);
    }
  }, [isActive, sessionStartTime]);

  // Format time for display
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Handle session start
  const handleStartSession = (sessionType) => {
    if (isActive) {
      endSession();
    }
    startSession(sessionType);
  };

  // Handle session end
  const handleEndSession = () => {
    endSession();
  };

  // Session button configurations
  const sessionButtons = [
    {
      type: SESSION_TYPES.FOCUS,
      icon: '🧠',
      label: 'Focus',
      description: 'Invest your time wisely'
    },
    {
      type: SESSION_TYPES.SHORT_BREAK,
      icon: '☕',
      label: 'Short Break',
      description: '5-15 minutes'
    },
    {
      type: SESSION_TYPES.LONG_BREAK,
      icon: '🛋️',
      label: 'Long Break',
      description: '15-30 minutes'
    }
  ];

  return (
    <ControlsContainer>
      <ControlsTitle>Session Controls</ControlsTitle>
      
      {isActive && (
        <SessionTimer>
          <TimerDisplay>{formatTime(sessionTime)}</TimerDisplay>
          <TimerLabel>
            {currentSession === SESSION_TYPES.FOCUS && 'Investing time in focus'}
            {currentSession === SESSION_TYPES.SHORT_BREAK && 'Taking a short break'}
            {currentSession === SESSION_TYPES.LONG_BREAK && 'Taking a long break'}
          </TimerLabel>
        </SessionTimer>
      )}
      
      <ButtonGrid>
        {sessionButtons.map((button) => (
          <SessionButton
            key={button.type}
            sessionType={button.type}
            isActive={isActive && currentSession === button.type}
            onClick={() => handleStartSession(button.type)}
            disabled={!isWithinDailyHours}
          >
            <ButtonIcon>{button.icon}</ButtonIcon>
            <ButtonLabel>
              <div>{button.label}</div>
              <div style={{ fontSize: '10px', opacity: 0.8 }}>
                {button.description}
              </div>
            </ButtonLabel>
          </SessionButton>
        ))}
      </ButtonGrid>
      
      {isActive && (
        <ActionButton
          variant="stop"
          onClick={handleEndSession}
        >
          End Session
        </ActionButton>
      )}
      
      {!isWithinDailyHours && (
        <div style={{ 
          textAlign: 'center', 
          color: 'var(--text-muted)', 
          fontSize: '14px',
          marginTop: '16px'
        }}>
          Sessions are only available during market hours (6:00 AM - 11:59 PM)
        </div>
      )}
    </ControlsContainer>
  );
}

export default FocusControls;
