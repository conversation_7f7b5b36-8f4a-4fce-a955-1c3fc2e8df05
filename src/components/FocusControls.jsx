import React, { useState, useEffect } from 'react';
import styled, { keyframes } from 'styled-components';
import { useTimeWallet, SESSION_TYPES } from '../context/TimeWalletContext';

const pulse = keyframes`
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 122, 255, 0.4);
  }
  70% {
    transform: scale(1.02);
    box-shadow: 0 0 0 20px rgba(0, 122, 255, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 122, 255, 0);
  }
`;

const ControlsContainer = styled.div`
  background: var(--bg-secondary);
  border-radius: 16px;
  padding: 24px;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-primary);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--border-secondary), transparent);
  }
`;

const ControlsTitle = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 20px 0;
  letter-spacing: -0.01em;
`;

const PomodoroStatus = styled.div`
  text-align: center;
  margin-bottom: 20px;
  padding: 16px;
  background: var(--bg-tertiary);
  border-radius: 12px;
  border: 1px solid var(--border-primary);
`;

const StatusText = styled.div`
  font-size: 16px;
  color: var(--text-secondary);
  margin-bottom: 8px;
`;

const CycleInfo = styled.div`
  font-size: 14px;
  color: var(--text-muted);
`;

const DurationSelector = styled.div`
  margin-bottom: 20px;
`;

const SelectorLabel = styled.label`
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 12px;
`;

const DurationOptions = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 12px;
`;

const DurationOption = styled.button`
  background: ${props => props.selected ? 'var(--accent-blue)' : 'var(--bg-quaternary)'};
  color: ${props => props.selected ? '#000' : 'var(--text-primary)'};
  border: 2px solid ${props => props.selected ? 'var(--accent-blue)' : 'var(--border-color)'};
  border-radius: 12px;
  padding: 12px 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const SessionTimer = styled.div`
  background: var(--bg-tertiary);
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  margin-bottom: 20px;
  border: 1px solid var(--border-primary);
`;

const TimerDisplay = styled.div`
  font-size: clamp(32px, 6vw, 48px);
  font-weight: 600;
  color: ${props => props.isOvertime ? 'var(--accent-warning)' : 'var(--text-primary)'};
  font-variant-numeric: tabular-nums;
  margin-bottom: 12px;
  letter-spacing: -0.025em;
  transition: color 0.3s ease;
`;

const TimerLabel = styled.div`
  font-size: 16px;
  color: var(--text-secondary);
  margin-bottom: 8px;
`;

const TimerSubtext = styled.div`
  font-size: 14px;
  color: var(--text-muted);
`;

const OvertimeIndicator = styled.div`
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.2);
  border-radius: 8px;
  padding: 8px 12px;
  margin-top: 12px;
  font-size: 13px;
  color: var(--accent-warning);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;

  &::before {
    content: '!';
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    background: var(--accent-warning);
    color: var(--bg-primary);
    border-radius: 50%;
    font-size: 10px;
    font-weight: 700;
  }
`;

const ActionButton = styled.button`
  width: 100%;
  background: ${props => {
    if (props.variant === 'stop') return 'var(--money-red)';
    if (props.variant === 'start') return 'var(--focus-accent)';
    return 'var(--money-green)';
  }};
  color: #fff;
  border: none;
  border-radius: 12px;
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: ${props => props.isActive ? pulse : 'none'} 2s infinite;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

function FocusControls() {
  const {
    currentSession,
    sessionStartTime,
    isActive,
    startSession,
    endSession,
    isWithinDailyHours,
    pomodoroState,
    overtimeTracking,
    updateOvertime
  } = useTimeWallet();

  const [sessionTime, setSessionTime] = useState(0);
  const [selectedDuration, setSelectedDuration] = useState(25);
  const [targetDuration, setTargetDuration] = useState(25 * 60); // in seconds
  const [hasNotified, setHasNotified] = useState(false);
  const [isOvertime, setIsOvertime] = useState(false);

  const durationOptions = [15, 25, 45, 60]; // minutes

  // Request notification permission on component mount
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  // Function to show notification
  const showNotification = (title, body, icon = null) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(title, {
        body,
        icon: icon || '/favicon.ico',
        badge: '/favicon.ico'
      });
    }
  };

  // Update session timer
  useEffect(() => {
    if (isActive && sessionStartTime) {
      const interval = setInterval(() => {
        const elapsed = Math.floor((Date.now() - sessionStartTime) / 1000);
        setSessionTime(elapsed);

        let sessionDuration;
        switch (currentSession) {
          case SESSION_TYPES.FOCUS:
            sessionDuration = targetDuration;
            break;
          case SESSION_TYPES.SHORT_BREAK:
            sessionDuration = 5 * 60;
            break;
          case SESSION_TYPES.LONG_BREAK:
            sessionDuration = 15 * 60;
            break;
          default:
            sessionDuration = 0;
        }

        // Check if session is in overtime
        const overtime = Math.max(0, elapsed - sessionDuration);
        setIsOvertime(overtime > 0);

        // Update overtime tracking
        if (overtime > 0) {
          updateOvertime({
            currentSessionOvertime: overtime,
            ...(currentSession === SESSION_TYPES.FOCUS
              ? { focusOvertime: (overtimeTracking?.focusOvertime || 0) + 1 }
              : { breakOvertime: (overtimeTracking?.breakOvertime || 0) + 1 }
            )
          });
        }

        // Show notification when session completes
        if (elapsed === sessionDuration && !hasNotified) {
          setHasNotified(true);

          if (currentSession === SESSION_TYPES.FOCUS) {
            showNotification(
              'Focus Session Complete!',
              `Great job! You completed a ${targetDuration / 60}-minute focus session.`
            );

            // Auto-start break after focus session
            setTimeout(() => {
              const isLongBreak = (pomodoroState?.completedFocusSessions || 0) % 4 === 3;
              const breakType = isLongBreak ? SESSION_TYPES.LONG_BREAK : SESSION_TYPES.SHORT_BREAK;
              const breakDuration = isLongBreak ? 15 : 5;

              showNotification(
                `${isLongBreak ? 'Long' : 'Short'} Break Started`,
                `Time for a ${breakDuration}-minute break. You've earned it!`
              );

              startSession(breakType);
            }, 2000);
          } else {
            const breakType = currentSession === SESSION_TYPES.SHORT_BREAK ? 'short' : 'long';
            showNotification(
              'Break Complete!',
              `Your ${breakType} break is over. Ready to focus again?`
            );
          }
        }
      }, 1000);

      return () => clearInterval(interval);
    } else {
      setSessionTime(0);
      setHasNotified(false);
      setIsOvertime(false);
    }
  }, [isActive, sessionStartTime, currentSession, targetDuration, hasNotified, endSession, startSession, pomodoroState, updateOvertime, overtimeTracking]);

  // Format time for display
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Get remaining time
  const getRemainingTime = () => {
    if (!isActive) return 0;

    let duration;
    switch (currentSession) {
      case SESSION_TYPES.FOCUS:
        duration = targetDuration;
        break;
      case SESSION_TYPES.SHORT_BREAK:
        duration = 5 * 60;
        break;
      case SESSION_TYPES.LONG_BREAK:
        duration = 15 * 60;
        break;
      default:
        duration = 0;
    }

    return Math.max(0, duration - sessionTime);
  };

  // Handle focus session start
  const handleStartFocus = () => {
    if (isActive) {
      endSession();
    }
    setTargetDuration(selectedDuration * 60);
    startSession(SESSION_TYPES.FOCUS, selectedDuration);
  };

  // Handle session end
  const handleEndSession = () => {
    endSession();
  };

  // Get session status text
  const getStatusText = () => {
    if (!isActive) {
      return `Ready to start ${selectedDuration}-minute focus session`;
    }

    switch (currentSession) {
      case SESSION_TYPES.FOCUS:
        return 'Focus Session Active - Investing your time wisely';
      case SESSION_TYPES.SHORT_BREAK:
        return 'Short Break - Recharge for the next session';
      case SESSION_TYPES.LONG_BREAK:
        return 'Long Break - Well deserved rest';
      default:
        return 'Ready to focus';
    }
  };

  const remainingTime = getRemainingTime();
  const completedSessions = pomodoroState?.completedFocusSessions || 0;
  const currentCycle = Math.floor(completedSessions / 4) + 1;
  const sessionsInCurrentCycle = completedSessions % 4;

  return (
    <ControlsContainer>
      <ControlsTitle>Pomodoro Focus Timer</ControlsTitle>

      <PomodoroStatus>
        <StatusText>{getStatusText()}</StatusText>
        <CycleInfo>
          Cycle {currentCycle} • Session {sessionsInCurrentCycle + 1}/4 • {completedSessions} completed today
        </CycleInfo>
      </PomodoroStatus>

      {!isActive && (
        <DurationSelector>
          <SelectorLabel>Focus Session Duration</SelectorLabel>
          <DurationOptions>
            {durationOptions.map((duration) => (
              <DurationOption
                key={duration}
                selected={selectedDuration === duration}
                onClick={() => setSelectedDuration(duration)}
                disabled={!isWithinDailyHours}
              >
                {duration}m
              </DurationOption>
            ))}
          </DurationOptions>
        </DurationSelector>
      )}

      {isActive && (
        <SessionTimer>
          <TimerDisplay isOvertime={isOvertime}>
            {isOvertime ? `+${formatTime(overtimeTracking?.currentSessionOvertime || 0)}` : formatTime(remainingTime)}
          </TimerDisplay>
          <TimerLabel>
            {isOvertime ? (
              currentSession === SESSION_TYPES.FOCUS ?
                `Focus session: ${targetDuration / 60} min + ${Math.floor((overtimeTracking?.currentSessionOvertime || 0) / 60)} min overtime` :
                `Break: ${currentSession === SESSION_TYPES.SHORT_BREAK ? '5' : '15'} min + ${Math.floor((overtimeTracking?.currentSessionOvertime || 0) / 60)} min overtime`
            ) : (
              <>
                {currentSession === SESSION_TYPES.FOCUS && 'Time remaining in focus session'}
                {currentSession === SESSION_TYPES.SHORT_BREAK && 'Short break time remaining'}
                {currentSession === SESSION_TYPES.LONG_BREAK && 'Long break time remaining'}
              </>
            )}
          </TimerLabel>
          <TimerSubtext>
            {currentSession === SESSION_TYPES.FOCUS && (isOvertime ? 'Additional invested time' : 'Money is being invested in your focus')}
            {currentSession !== SESSION_TYPES.FOCUS && (isOvertime ? 'Extended break time' : 'Break time - no money deduction')}
          </TimerSubtext>
          {isOvertime && (
            <OvertimeIndicator>
              Session is running overtime
            </OvertimeIndicator>
          )}
        </SessionTimer>
      )}

      {!isActive ? (
        <ActionButton
          variant="start"
          onClick={handleStartFocus}
          disabled={!isWithinDailyHours}
          isActive={false}
        >
          Start {selectedDuration}-Minute Focus Session
        </ActionButton>
      ) : (
        <ActionButton
          variant="stop"
          onClick={handleEndSession}
          isActive={false}
        >
          End Current Session
        </ActionButton>
      )}

      {!isWithinDailyHours && (
        <div style={{
          textAlign: 'center',
          color: 'var(--text-muted)',
          fontSize: '14px',
          marginTop: '16px'
        }}>
          Focus sessions are only available during market hours (6:00 AM - 11:59 PM)
        </div>
      )}
    </ControlsContainer>
  );
}

export default FocusControls;
