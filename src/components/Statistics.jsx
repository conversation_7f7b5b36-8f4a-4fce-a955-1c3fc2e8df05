import React from 'react';
import styled from 'styled-components';
import { useTimeWallet } from '../context/TimeWalletContext';

const StatsContainer = styled.div`
  background: linear-gradient(145deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  border-radius: 24px;
  padding: 40px;
  box-shadow: var(--shadow-heavy);
  border: 1px solid var(--border-light);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--border-light), transparent);
  }
`;

const StatsTitle = styled.h3`
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 32px 0;
  letter-spacing: -0.01em;
`;

const StatCard = styled.div`
  background-color: var(--bg-tertiary);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  border: 1px solid var(--border-color);
`;

const StatHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const StatLabel = styled.div`
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
`;

const StatIcon = styled.div`
  font-size: 20px;
`;

const StatValue = styled.div`
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  font-variant-numeric: tabular-nums;
`;

const StatSubtext = styled.div`
  font-size: 12px;
  color: var(--text-muted);
  margin-top: 4px;
`;

const MoneyBreakdown = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-top: 20px;
`;

const MoneyCard = styled.div`
  background-color: var(--bg-tertiary);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  border: 1px solid var(--border-color);
`;

const MoneyAmount = styled.div`
  font-size: 20px;
  font-weight: 700;
  color: ${props => props.type === 'invested' ? 'var(--accent-green)' : 'var(--accent-red)'};
  margin-bottom: 4px;
`;

const MoneyLabel = styled.div`
  font-size: 12px;
  color: var(--text-secondary);
`;

const ProgressSection = styled.div`
  margin-top: 20px;
`;

const ProgressItem = styled.div`
  margin-bottom: 16px;
`;

const ProgressHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const ProgressLabel = styled.div`
  font-size: 14px;
  color: var(--text-secondary);
`;

const ProgressValue = styled.div`
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 600;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 6px;
  background-color: var(--bg-primary);
  border-radius: 3px;
  overflow: hidden;
`;

const ProgressFill = styled.div`
  height: 100%;
  width: ${props => props.percentage}%;
  background-color: ${props => props.color};
  border-radius: 3px;
  transition: width 0.5s ease-out;
`;

const SummaryCard = styled.div`
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-green));
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
  color: #000;
`;

const SummaryTitle = styled.div`
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
`;

const SummaryText = styled.div`
  font-size: 14px;
  opacity: 0.8;
`;

function Statistics() {
  const {
    dailyStats,
    balance,
    constants,
    overtimeTracking
  } = useTimeWallet();

  // Calculate total time spent
  const totalTimeMinutes = dailyStats.focusTime + dailyStats.shortBreakTime + dailyStats.longBreakTime;
  
  // Calculate efficiency percentage
  const totalPossibleMinutes = 18 * 60; // 18 hours in minutes
  const efficiencyPercentage = totalTimeMinutes > 0 ? (dailyStats.focusTime / totalTimeMinutes) * 100 : 0;
  
  // Calculate money saved/lost
  const totalMoneySpent = dailyStats.moneyInvested + dailyStats.moneyWasted;
  const moneyRemaining = constants.STARTING_BALANCE - totalMoneySpent;

  // Format time for display
  const formatTime = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  // Get performance message
  const getPerformanceMessage = () => {
    if (efficiencyPercentage >= 80) {
      return "Excellent focus! You're making great investments with your time.";
    } else if (efficiencyPercentage >= 60) {
      return "Good focus! Keep investing your time wisely.";
    } else if (efficiencyPercentage >= 40) {
      return "Room for improvement. Try to focus more to maximize your time investment.";
    } else {
      return "Consider focusing more to get better returns on your time investment.";
    }
  };

  return (
    <StatsContainer>
      <StatsTitle>Daily Statistics</StatsTitle>
      
      <StatCard>
        <StatHeader>
          <StatLabel>Total Sessions</StatLabel>
          <StatIcon>•</StatIcon>
        </StatHeader>
        <StatValue>{dailyStats.totalSessions}</StatValue>
        <StatSubtext>Sessions completed today</StatSubtext>
      </StatCard>

      <StatCard>
        <StatHeader>
          <StatLabel>Focus Efficiency</StatLabel>
          <StatIcon>%</StatIcon>
        </StatHeader>
        <StatValue>{efficiencyPercentage.toFixed(1)}%</StatValue>
        <StatSubtext>Time spent in focus vs. breaks</StatSubtext>
      </StatCard>

      <StatCard>
        <StatHeader>
          <StatLabel>Overtime</StatLabel>
          <StatIcon>+</StatIcon>
        </StatHeader>
        <StatValue>{Math.floor((overtimeTracking?.focusOvertime || 0) / 60)}m</StatValue>
        <StatSubtext>Additional focus time invested</StatSubtext>
      </StatCard>

      <MoneyBreakdown>
        <MoneyCard>
          <MoneyAmount type="invested">${dailyStats.moneyInvested.toFixed(2)}</MoneyAmount>
          <MoneyLabel>Invested</MoneyLabel>
        </MoneyCard>
        <MoneyCard>
          <MoneyAmount type="wasted">${dailyStats.moneyWasted.toFixed(2)}</MoneyAmount>
          <MoneyLabel>Wasted</MoneyLabel>
        </MoneyCard>
      </MoneyBreakdown>

      <ProgressSection>
        <ProgressItem>
          <ProgressHeader>
            <ProgressLabel>Focus Time</ProgressLabel>
            <ProgressValue>{formatTime(dailyStats.focusTime)}</ProgressValue>
          </ProgressHeader>
          <ProgressBar>
            <ProgressFill 
              percentage={totalTimeMinutes > 0 ? (dailyStats.focusTime / totalTimeMinutes) * 100 : 0}
              color="var(--accent-green)"
            />
          </ProgressBar>
        </ProgressItem>

        <ProgressItem>
          <ProgressHeader>
            <ProgressLabel>Short Breaks</ProgressLabel>
            <ProgressValue>{formatTime(dailyStats.shortBreakTime)}</ProgressValue>
          </ProgressHeader>
          <ProgressBar>
            <ProgressFill 
              percentage={totalTimeMinutes > 0 ? (dailyStats.shortBreakTime / totalTimeMinutes) * 100 : 0}
              color="var(--accent-orange)"
            />
          </ProgressBar>
        </ProgressItem>

        <ProgressItem>
          <ProgressHeader>
            <ProgressLabel>Long Breaks</ProgressLabel>
            <ProgressValue>{formatTime(dailyStats.longBreakTime)}</ProgressValue>
          </ProgressHeader>
          <ProgressBar>
            <ProgressFill 
              percentage={totalTimeMinutes > 0 ? (dailyStats.longBreakTime / totalTimeMinutes) * 100 : 0}
              color="var(--accent-blue)"
            />
          </ProgressBar>
        </ProgressItem>
      </ProgressSection>

      <SummaryCard>
        <SummaryTitle>Performance Summary</SummaryTitle>
        <SummaryText>{getPerformanceMessage()}</SummaryText>
      </SummaryCard>
    </StatsContainer>
  );
}

export default Statistics;
