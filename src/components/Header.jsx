import React from 'react';
import styled from 'styled-components';
import { useTimeWallet } from '../context/TimeWalletContext';

const HeaderContainer = styled.header`
  width: 100%;
  max-width: 1200px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid var(--border-color);
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const LogoIcon = styled.div`
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--accent-green) 0%, var(--accent-blue) 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  color: white;
`;

const LogoText = styled.h1`
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
`;

const TimeInfo = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
`;

const CurrentTime = styled.div`
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
`;

const DailyHours = styled.div`
  font-size: 14px;
  color: var(--text-secondary);
`;

const StatusIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: ${props => props.isActive ? 'var(--accent-green)' : 'var(--text-muted)'};
`;

const StatusDot = styled.div`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: ${props => props.isActive ? 'var(--accent-green)' : 'var(--text-muted)'};
  animation: ${props => props.isActive ? 'pulse 2s infinite' : 'none'};

  @keyframes pulse {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      opacity: 1;
    }
  }
`;

function Header() {
  const { isWithinDailyHours, constants } = useTimeWallet();
  const [currentTime, setCurrentTime] = React.useState(new Date());

  React.useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    });
  };

  return (
    <HeaderContainer>
      <Logo>
        <LogoIcon>💰</LogoIcon>
        <LogoText>TimeWallet</LogoText>
      </Logo>
      
      <TimeInfo>
        <CurrentTime>{formatTime(currentTime)}</CurrentTime>
        <DailyHours>
          Active: {constants.DAILY_START_HOUR}:00 AM - {constants.DAILY_END_HOUR}:59 PM
        </DailyHours>
        <StatusIndicator isActive={isWithinDailyHours}>
          <StatusDot isActive={isWithinDailyHours} />
          {isWithinDailyHours ? 'Market Open' : 'Market Closed'}
        </StatusIndicator>
      </TimeInfo>
    </HeaderContainer>
  );
}

export default Header;
