import e from"css-to-react-native";import{parse as t}from"postcss";import{__spreadArray as n,__assign as r}from"tslib";import o,{useMemo as i,useContext as s,createElement as a}from"react";import"shallowequal";import*as c from"stylis";import u from"@emotion/unitless";var l=Object.freeze([]),p=Object.freeze({}),h="production"!==process.env.NODE_ENV?{1:"Cannot create styled-component for component: %s.\n\n",2:"Can't collect styles once you've consumed a `ServerStyleSheet`'s styles! `ServerStyleSheet` is a one off instance for each server-side render cycle.\n\n- Are you trying to reuse it across renders?\n- Are you accidentally calling collectStyles twice?\n\n",3:"Streaming SSR is only supported in a Node.js environment; Please do not try to call this method in the browser.\n\n",4:"The `StyleSheetManager` expects a valid target or sheet prop!\n\n- Does this error occur on the client and is your target falsy?\n- Does this error occur on the server and is the sheet falsy?\n\n",5:"The clone method cannot be used on the client!\n\n- Are you running in a client-like environment on the server?\n- Are you trying to run SSR on the client?\n\n",6:"Trying to insert a new style tag, but the given Node is unmounted!\n\n- Are you using a custom target that isn't mounted?\n- Does your document not have a valid head element?\n- Have you accidentally removed a style tag manually?\n\n",7:'ThemeProvider: Please return an object from your "theme" prop function, e.g.\n\n```js\ntheme={() => ({})}\n```\n\n',8:'ThemeProvider: Please make your "theme" prop an object.\n\n',9:"Missing document `<head>`\n\n",10:"Cannot find a StyleSheet instance. Usually this happens if there are multiple copies of styled-components loaded at once. Check out this issue for how to troubleshoot and fix the common cases where this situation can happen: https://github.com/styled-components/styled-components/issues/1941#issuecomment-417862021\n\n",11:"_This error was replaced with a dev-time warning, it will be deleted for v4 final._ [createGlobalStyle] received children which will not be rendered. Please use the component without passing children elements.\n\n",12:"It seems you are interpolating a keyframe declaration (%s) into an untagged string. This was supported in styled-components v3, but is not longer supported in v4 as keyframes are now injected on-demand. Please wrap your string in the css\\`\\` helper which ensures the styles are injected correctly. See https://www.styled-components.com/docs/api#css\n\n",13:"%s is not a styled component and cannot be referred to via component selector. See https://www.styled-components.com/docs/advanced#referring-to-other-components for more details.\n\n",14:'ThemeProvider: "theme" prop is required.\n\n',15:"A stylis plugin has been supplied that is not named. We need a name for each plugin to be able to prevent styling collisions between different stylis configurations within the same app. Before you pass your plugin to `<StyleSheetManager stylisPlugins={[]}>`, please make sure each plugin is uniquely-named, e.g.\n\n```js\nObject.defineProperty(importedPlugin, 'name', { value: 'some-unique-name' });\n```\n\n",16:"Reached the limit of how many styled components may be created at group %s.\nYou may only create up to 1,073,741,824 components. If you're creating components dynamically,\nas for instance in your render method then you may be running into this limitation.\n\n",17:"CSSStyleSheet could not be found on HTMLStyleElement.\nHas styled-components' style tag been unmounted or altered by another script?\n",18:"ThemeProvider: Please make sure your useTheme hook is within a `<ThemeProvider>`"}:{};function f(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=e[0],r=[],o=1,i=e.length;o<i;o+=1)r.push(e[o]);return r.forEach(function(e){n=n.replace(/%[a-z]/,e)}),n}function d(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];return"production"===process.env.NODE_ENV?new Error("An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#".concat(e," for more information.").concat(t.length>0?" Args: ".concat(t.join(", ")):"")):new Error(f.apply(void 0,n([h[e]],t,!1)).trim())}function y(e,t){Object.defineProperty(e,"toString",{value:t})}var m="undefined"!=typeof process&&void 0!==process.env&&(process.env.REACT_APP_SC_ATTR||process.env.SC_ATTR)||"data-styled",v="active",g="data-styled-version",S="6.1.19",w="/*!sc*/\n",b="undefined"!=typeof window&&"undefined"!=typeof document,E=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&void 0!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&""!==process.env.REACT_APP_SC_DISABLE_SPEEDY?"false"!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&process.env.REACT_APP_SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&void 0!==process.env.SC_DISABLE_SPEEDY&&""!==process.env.SC_DISABLE_SPEEDY?"false"!==process.env.SC_DISABLE_SPEEDY&&process.env.SC_DISABLE_SPEEDY:"production"!==process.env.NODE_ENV),P=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}return e.prototype.indexOfGroup=function(e){for(var t=0,n=0;n<e;n++)t+=this.groupSizes[n];return t},e.prototype.insertRules=function(e,t){if(e>=this.groupSizes.length){for(var n=this.groupSizes,r=n.length,o=r;e>=o;)if((o<<=1)<0)throw d(16,"".concat(e));this.groupSizes=new Uint32Array(o),this.groupSizes.set(n),this.length=o;for(var i=r;i<o;i++)this.groupSizes[i]=0}for(var s=this.indexOfGroup(e+1),a=(i=0,t.length);i<a;i++)this.tag.insertRule(s,t[i])&&(this.groupSizes[e]++,s++)},e.prototype.clearGroup=function(e){if(e<this.length){var t=this.groupSizes[e],n=this.indexOfGroup(e),r=n+t;this.groupSizes[e]=0;for(var o=n;o<r;o++)this.tag.deleteRule(n)}},e.prototype.getGroup=function(e){var t="";if(e>=this.length||0===this.groupSizes[e])return t;for(var n=this.groupSizes[e],r=this.indexOfGroup(e),o=r+n,i=r;i<o;i++)t+="".concat(this.tag.getRule(i)).concat(w);return t},e}(),A=1<<30,_=new Map,N=new Map,C=1,T=function(e){if(_.has(e))return _.get(e);for(;N.has(C);)C++;var t=C++;if("production"!==process.env.NODE_ENV&&((0|t)<0||t>A))throw d(16,"".concat(t));return _.set(e,t),N.set(t,e),t},D=function(e,t){C=t+1,_.set(e,t),N.set(t,e)},O="style[".concat(m,"][").concat(g,'="').concat(S,'"]'),R=new RegExp("^".concat(m,'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)')),j=function(e,t,n){for(var r,o=n.split(","),i=0,s=o.length;i<s;i++)(r=o[i])&&e.registerName(t,r)},x=function(e,t){for(var n,r=(null!==(n=t.textContent)&&void 0!==n?n:"").split(w),o=[],i=0,s=r.length;i<s;i++){var a=r[i].trim();if(a){var c=a.match(R);if(c){var u=0|parseInt(c[1],10),l=c[2];0!==u&&(D(l,u),j(e,l,c[3]),e.getTag().insertRules(u,o)),o.length=0}else o.push(a)}}},I=function(e){for(var t=document.querySelectorAll(O),n=0,r=t.length;n<r;n++){var o=t[n];o&&o.getAttribute(m)!==v&&(x(e,o),o.parentNode&&o.parentNode.removeChild(o))}},B=function(e){var t=document.head,n=e||t,r=document.createElement("style"),o=function(e){var t=Array.from(e.querySelectorAll("style[".concat(m,"]")));return t[t.length-1]}(n),i=void 0!==o?o.nextSibling:null;r.setAttribute(m,v),r.setAttribute(g,S);var s="undefined"!=typeof __webpack_nonce__?__webpack_nonce__:null;return s&&r.setAttribute("nonce",s),n.insertBefore(r,i),r},z=function(){function e(e){this.element=B(e),this.element.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var t=document.styleSheets,n=0,r=t.length;n<r;n++){var o=t[n];if(o.ownerNode===e)return o}throw d(17)}(this.element),this.length=0}return e.prototype.insertRule=function(e,t){try{return this.sheet.insertRule(t,e),this.length++,!0}catch(e){return!1}},e.prototype.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},e.prototype.getRule=function(e){var t=this.sheet.cssRules[e];return t&&t.cssText?t.cssText:""},e}(),L=function(){function e(e){this.element=B(e),this.nodes=this.element.childNodes,this.length=0}return e.prototype.insertRule=function(e,t){if(e<=this.length&&e>=0){var n=document.createTextNode(t);return this.element.insertBefore(n,this.nodes[e]||null),this.length++,!0}return!1},e.prototype.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},e.prototype.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),V=function(){function e(e){this.rules=[],this.length=0}return e.prototype.insertRule=function(e,t){return e<=this.length&&(this.rules.splice(e,0,t),this.length++,!0)},e.prototype.deleteRule=function(e){this.rules.splice(e,1),this.length--},e.prototype.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),k=b,$={isServer:!b,useCSSOMInjection:!E},F=function(){function e(e,t,n){void 0===e&&(e=p),void 0===t&&(t={});var o=this;this.options=r(r({},$),e),this.gs=t,this.names=new Map(n),this.server=!!e.isServer,!this.server&&b&&k&&(k=!1,I(this)),y(this,function(){return function(e){for(var t=e.getTag(),n=t.length,r="",o=function(n){var o=function(e){return N.get(e)}(n);if(void 0===o)return"continue";var i=e.names.get(o),s=t.getGroup(n);if(void 0===i||!i.size||0===s.length)return"continue";var a="".concat(m,".g").concat(n,'[id="').concat(o,'"]'),c="";void 0!==i&&i.forEach(function(e){e.length>0&&(c+="".concat(e,","))}),r+="".concat(s).concat(a,'{content:"').concat(c,'"}').concat(w)},i=0;i<n;i++)o(i);return r}(o)})}return e.registerId=function(e){return T(e)},e.prototype.rehydrate=function(){!this.server&&b&&I(this)},e.prototype.reconstructWithOptions=function(t,n){return void 0===n&&(n=!0),new e(r(r({},this.options),t),this.gs,n&&this.names||void 0)},e.prototype.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},e.prototype.getTag=function(){return this.tag||(this.tag=(e=function(e){var t=e.useCSSOMInjection,n=e.target;return e.isServer?new V(n):t?new z(n):new L(n)}(this.options),new P(e)));var e},e.prototype.hasNameForId=function(e,t){return this.names.has(e)&&this.names.get(e).has(t)},e.prototype.registerName=function(e,t){if(T(e),this.names.has(e))this.names.get(e).add(t);else{var n=new Set;n.add(t),this.names.set(e,n)}},e.prototype.insertRules=function(e,t,n){this.registerName(e,t),this.getTag().insertRules(T(e),n)},e.prototype.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},e.prototype.clearRules=function(e){this.getTag().clearGroup(T(e)),this.clearNames(e)},e.prototype.clearTag=function(){this.tag=void 0},e}(),Y=function(e,t){for(var n=t.length;n;)e=33*e^t.charCodeAt(--n);return e},M=/&/g,G=/^\s*\/\/.*$/gm;function W(e,t){return e.map(function(e){return"rule"===e.type&&(e.value="".concat(t," ").concat(e.value),e.value=e.value.replaceAll(",",",".concat(t," ")),e.props=e.props.map(function(e){return"".concat(t," ").concat(e)})),Array.isArray(e.children)&&"@keyframes"!==e.type&&(e.children=W(e.children,t)),e})}var q=new F,H=function(e){var t,n,r,o=p.options,i=void 0===o?p:o,s=p.plugins,a=void 0===s?l:s,u=function(e,r,o){return o.startsWith(n)&&o.endsWith(n)&&o.replaceAll(n,"").length>0?".".concat(t):e},h=a.slice();h.push(function(e){e.type===c.RULESET&&e.value.includes("&")&&(e.props[0]=e.props[0].replace(M,n).replace(r,u))}),i.prefix&&h.push(c.prefixer),h.push(c.stringify);var f=function(e,o,s,a){void 0===o&&(o=""),void 0===s&&(s=""),void 0===a&&(a="&"),t=a,n=o,r=new RegExp("\\".concat(n,"\\b"),"g");var u=e.replace(G,""),l=c.compile(s||o?"".concat(s," ").concat(o," { ").concat(u," }"):u);i.namespace&&(l=W(l,i.namespace));var p=[];return c.serialize(l,c.middleware(h.concat(c.rulesheet(function(e){return p.push(e)})))),p};return f.hash=a.length?a.reduce(function(e,t){return t.name||d(15),Y(e,t.name)},5381).toString():"",f}(),U=(o.createContext({shouldForwardProp:void 0,styleSheet:q,stylis:H}),o.createContext(void 0),function(){function e(e,t){var n=this;this.inject=function(e,t){void 0===t&&(t=H);var r=n.name+t.hash;e.hasNameForId(n.id,r)||e.insertRules(n.id,r,t(n.rules,r,"@keyframes"))},this.name=e,this.id="sc-keyframes-".concat(e),this.rules=t,y(this,function(){throw d(12,String(n.name))})}return e.prototype.getName=function(e){return void 0===e&&(e=H),this.name+e.hash},e}());function K(e){return"production"!==process.env.NODE_ENV&&"string"==typeof e&&e||e.displayName||e.name||"Component"}var Z=function(e){return e>="A"&&e<="Z"};function J(e){for(var t="",n=0;n<e.length;n++){var r=e[n];if(1===n&&"-"===r&&"-"===e[0])return e;Z(r)?t+="-"+r.toLowerCase():t+=r}return t.startsWith("ms-")?"-"+t:t}function Q(e){return"function"==typeof e}function X(e){return null!==e&&"object"==typeof e&&e.constructor.name===Object.name&&!("props"in e&&e.$$typeof)}function ee(e){return"object"==typeof e&&"styledComponentId"in e}var te=function(e){return null==e||!1===e||""===e},ne=function(e){var t,r,o=[];for(var i in e){var s=e[i];e.hasOwnProperty(i)&&!te(s)&&(Array.isArray(s)&&s.isCss||Q(s)?o.push("".concat(J(i),":"),s,";"):X(s)?o.push.apply(o,n(n(["".concat(i," {")],ne(s),!1),["}"],!1)):o.push("".concat(J(i),": ").concat((t=i,null==(r=s)||"boolean"==typeof r||""===r?"":"number"!=typeof r||0===r||t in u||t.startsWith("--")?String(r).trim():"".concat(r,"px")),";")))}return o};function re(e,t,n,r){if(te(e))return[];if(ee(e))return[".".concat(e.styledComponentId)];if(Q(e)){if(!Q(i=e)||i.prototype&&i.prototype.isReactComponent||!t)return[e];var o=e(t);return"production"===process.env.NODE_ENV||"object"!=typeof o||Array.isArray(o)||o instanceof U||X(o)||null===o||console.error("".concat(K(e)," is not a styled component and cannot be referred to via component selector. See https://www.styled-components.com/docs/advanced#referring-to-other-components for more details.")),re(o,t,n,r)}var i;return e instanceof U?n?(e.inject(n,r),[e.getName(r)]):[e]:X(e)?ne(e):Array.isArray(e)?Array.prototype.concat.apply(l,e.map(function(e){return re(e,t,n,r)})):[e.toString()]}function oe(e,t){for(var n=[e[0]],r=0,o=t.length;r<o;r+=1)n.push(t[r],e[r+1]);return n}var ie=function(e){return Object.assign(e,{isCss:!0})};function se(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(Q(e)||X(e))return ie(re(oe(l,n([e],t,!0))));var o=e;return 0===t.length&&1===o.length&&"string"==typeof o[0]?re(o):ie(re(oe(o,t)))}function ae(e,t,o){if(void 0===o&&(o=p),!t)throw d(1,t);var i=function(r){for(var i=[],s=1;s<arguments.length;s++)i[s-1]=arguments[s];return e(t,o,se.apply(void 0,n([r],i,!1)))};return i.attrs=function(n){return ae(e,t,r(r({},o),{attrs:Array.prototype.concat(o.attrs,n).filter(Boolean)}))},i.withConfig=function(n){return ae(e,t,r(r({},o),n))},i}var ce,ue=o.createContext(void 0),le=ue.Consumer;function pe(){var e=s(ue);if(!e)throw d(18);return e}function he(e){var t=o.useContext(ue),n=i(function(){return function(e,t){if(!e)throw d(14);if(Q(e)){var n=e(t);if("production"!==process.env.NODE_ENV&&(null===n||Array.isArray(n)||"object"!=typeof n))throw d(7);return n}if(Array.isArray(e)||"object"!=typeof e)throw d(8);return t?r(r({},t),e):e}(e.theme,t)},[e.theme,t]);return e.children?o.createElement(ue.Provider,{value:n},e.children):null}function fe(e,t,n){return void 0===n&&(n=p),e.theme!==n.theme&&e.theme||t||n.theme}var de="function"==typeof Symbol&&Symbol.for,ye=de?Symbol.for("react.memo"):60115,me=de?Symbol.for("react.forward_ref"):60112,ve={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},ge={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Se={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},we=((ce={})[me]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},ce[ye]=Se,ce);function be(e){return("type"in(t=e)&&t.type.$$typeof)===ye?Se:"$$typeof"in e?we[e.$$typeof]:ve;var t}var Ee=Object.defineProperty,Pe=Object.getOwnPropertyNames,Ae=Object.getOwnPropertySymbols,_e=Object.getOwnPropertyDescriptor,Ne=Object.getPrototypeOf,Ce=Object.prototype;function Te(e,t,n){if("string"!=typeof t){if(Ce){var r=Ne(t);r&&r!==Ce&&Te(e,r,n)}var o=Pe(t);Ae&&(o=o.concat(Ae(t)));for(var i=be(e),s=be(t),a=0;a<o.length;++a){var c=o[a];if(!(c in ge||n&&n[c]||s&&c in s||i&&c in i)){var u=_e(t,c);try{Ee(e,c,u)}catch(e){}}}}return e}function De(e){var t=o.forwardRef(function(t,n){var i=fe(t,o.useContext(ue),e.defaultProps);return"production"!==process.env.NODE_ENV&&void 0===i&&console.warn('[withTheme] You are not using a ThemeProvider nor passing a theme prop or a theme in defaultProps in component class "'.concat(K(e),'"')),o.createElement(e,r({},t,{theme:i,ref:n}))});return t.displayName="WithTheme(".concat(K(e),")"),Te(t,e)}var Oe=/(a)(d)/gi,Re=function(e){return String.fromCharCode(e+(e>25?39:97))};function je(e,t){if(0===e.length)return"";for(var n=e[0],r=1;r<e.length;r++)n+=t?t+e[r]:e[r];return n}var xe=["fit-content","min-content","max-content"],Ie={};function Be(e,t,n){if(void 0===n&&(n=!1),!n&&!X(e)&&!Array.isArray(e))return t;if(Array.isArray(t))for(var r=0;r<t.length;r++)e[r]=Be(e[r],t[r]);else if(X(t))for(var r in t)e[r]=Be(e[r],t[r]);return e}var ze,Le,Ve=require("react-native"),ke=(ze=Ve.StyleSheet,Le=function(){function n(e){this.rules=e}return n.prototype.generateStyleObject=function(n){var r=je(re(this.rules,n)),o=function(e){var t,n="";for(t=Math.abs(e);t>52;t=t/52|0)n=Re(t%52)+n;return(Re(t%52)+n).replace(Oe,"$1-$2")}(Y(5381,r)>>>0);if(!Ie[o]){var i=t(r),s=[];i.each(function(e){if("decl"===e.type){if(xe.includes(e.value))return void("production"!==process.env.NODE_ENV&&console.warn('[styled-components/native] The value "'.concat(e.value,'" for property "').concat(e.prop,'" is not supported in React Native and will be ignored.')));s.push([e.prop,e.value])}else"production"!==process.env.NODE_ENV&&"comment"!==e.type&&console.warn("Node of type ".concat(e.type," not supported as an inline style"))});var a=e(s,["borderWidth","borderColor"]),c=ze.create({generated:a});Ie[o]=c.generated}return Ie[o]},n}(),function(e,t,n){var s=ee(e),c=e,u=t.displayName,h=void 0===u?function(e){return function(e){return"string"==typeof e&&("production"===process.env.NODE_ENV||e.charAt(0)===e.charAt(0).toLowerCase())}(e)?"styled.".concat(e):"Styled(".concat(K(e),")")}(e):u,f=t.attrs,d=void 0===f?l:f,y=s&&c.attrs?c.attrs.concat(d).filter(Boolean):d,m=t.shouldForwardProp;if(s&&c.shouldForwardProp){var v=c.shouldForwardProp;if(t.shouldForwardProp){var g=t.shouldForwardProp;m=function(e,t){return v(e,t)&&g(e,t)}}else m=v}var S=function(e,t){return function(e,t,n){var s=e.attrs,c=e.inlineStyle,u=e.defaultProps,l=e.shouldForwardProp,h=e.target,f=o.useContext(ue),d=function(e,t,n){void 0===e&&(e=p);var o=r(r({},t),{theme:e}),i={};return n.forEach(function(e){var t,n=Q(e)?e(o):e;for(t in n)o[t]=i[t]=n[t]}),[o,i]}(fe(t,f,u)||p,t,s),y=d[1],m=c.generateStyleObject(d[0]),v=n,g=y.as||t.as||h,S=y!==t?r(r({},t),y):t,w={};for(var b in S)"$"!==b[0]&&"as"!==b&&("forwardedAs"===b?w.as=S[b]:l&&!l(b,g)||(w[b]=S[b]));return w.style=i(function(){return Q(t.style)?function(e){return[m].concat(t.style(e))}:t.style?[m].concat(t.style):m},[t.style,m]),n&&(w.ref=v),a(g,w)}(w,e,t)};S.displayName=h;var w=o.forwardRef(S);return w.attrs=y,w.inlineStyle=new Le(s?c.inlineStyle.rules.concat(n):n),w.displayName=h,w.shouldForwardProp=m,w.styledComponentId=!0,w.target=s?c.target:e,Object.defineProperty(w,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(e){this._foldedDefaultProps=s?function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var r=0,o=t;r<o.length;r++)Be(e,o[r],!0);return e}({},c.defaultProps,e):e}}),Te(w,e,{attrs:!0,inlineStyle:!0,displayName:!0,shouldForwardProp:!0,target:!0}),w}),$e=function(e){return ae(ke,e)};["ActivityIndicator","Button","DatePickerIOS","DrawerLayoutAndroid","FlatList","Image","ImageBackground","KeyboardAvoidingView","Modal","Pressable","ProgressBarAndroid","ProgressViewIOS","RefreshControl","SafeAreaView","ScrollView","SectionList","Slider","Switch","Text","TextInput","TouchableHighlight","TouchableOpacity","View","VirtualizedList"].forEach(function(e){return Object.defineProperty($e,e,{enumerable:!0,configurable:!1,get:function(){if(e in Ve&&Ve[e])return $e(Ve[e]);throw new Error("".concat(e," is not available in the currently-installed version of react-native"))}})});var Fe=function(n){var r=je(re(n)),o=t(r),i=[];o.each(function(e){"decl"===e.type?i.push([e.prop,e.value]):"production"!==process.env.NODE_ENV&&"comment"!==e.type&&console.warn("Node of type ".concat(e.type," not supported as an inline style"))});var s=e(i,["borderWidth","borderColor"]);return Ve.StyleSheet.create({style:s}).style};export{le as ThemeConsumer,ue as ThemeContext,he as ThemeProvider,se as css,$e as default,ee as isStyledComponent,$e as styled,Fe as toStyleSheet,pe as useTheme,De as withTheme};
//# sourceMappingURL=styled-components.native.esm.js.map
