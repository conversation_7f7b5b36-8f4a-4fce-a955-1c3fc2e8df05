!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define(["react"],t):(e=e||self).styled=t(e.React)}(this,function(e){"use strict";var t="undefined"!=typeof process&&void 0!==process.env&&(process.env.REACT_APP_SC_ATTR||process.env.SC_ATTR)||"data-styled",r="active",n="data-styled-version",o="6.1.19",s="/*!sc*/\n",a="undefined"!=typeof window&&"undefined"!=typeof document,i=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&void 0!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&""!==process.env.REACT_APP_SC_DISABLE_SPEEDY?"false"!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&process.env.REACT_APP_SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&void 0!==process.env.SC_DISABLE_SPEEDY&&""!==process.env.SC_DISABLE_SPEEDY&&"false"!==process.env.SC_DISABLE_SPEEDY&&process.env.SC_DISABLE_SPEEDY),c={},u=function(){return u=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},u.apply(this,arguments)};function l(e,t,r){if(r||2===arguments.length)for(var n,o=0,s=t.length;o<s;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}"function"==typeof SuppressedError&&SuppressedError;var f=Object.freeze([]),p=Object.freeze({});function h(e,t){Object.defineProperty(e,"toString",{value:t})}function d(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];return new Error("An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#".concat(e," for more information.").concat(t.length>0?" Args: ".concat(t.join(", ")):""))}var m=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}return e.prototype.indexOfGroup=function(e){for(var t=0,r=0;r<e;r++)t+=this.groupSizes[r];return t},e.prototype.insertRules=function(e,t){if(e>=this.groupSizes.length){for(var r=this.groupSizes,n=r.length,o=n;e>=o;)if((o<<=1)<0)throw d(16,"".concat(e));this.groupSizes=new Uint32Array(o),this.groupSizes.set(r),this.length=o;for(var s=n;s<o;s++)this.groupSizes[s]=0}for(var a=this.indexOfGroup(e+1),i=(s=0,t.length);s<i;s++)this.tag.insertRule(a,t[s])&&(this.groupSizes[e]++,a++)},e.prototype.clearGroup=function(e){if(e<this.length){var t=this.groupSizes[e],r=this.indexOfGroup(e),n=r+t;this.groupSizes[e]=0;for(var o=r;o<n;o++)this.tag.deleteRule(r)}},e.prototype.getGroup=function(e){var t="";if(e>=this.length||0===this.groupSizes[e])return t;for(var r=this.groupSizes[e],n=this.indexOfGroup(e),o=n+r,a=n;a<o;a++)t+="".concat(this.tag.getRule(a)).concat(s);return t},e}(),v=new Map,y=new Map,g=1,S=function(e){if(v.has(e))return v.get(e);for(;y.has(g);)g++;var t=g++;return v.set(e,t),y.set(t,e),t},b=function(e,t){g=t+1,v.set(e,t),y.set(t,e)},w="style[".concat(t,"][").concat(n,'="').concat(o,'"]'),C=new RegExp("^".concat(t,'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)')),I=function(e,t,r){for(var n,o=r.split(","),s=0,a=o.length;s<a;s++)(n=o[s])&&e.registerName(t,n)},P=function(e,t){for(var r,n=(null!==(r=t.textContent)&&void 0!==r?r:"").split(s),o=[],a=0,i=n.length;a<i;a++){var c=n[a].trim();if(c){var u=c.match(C);if(u){var l=0|parseInt(u[1],10),f=u[2];0!==l&&(b(f,l),I(e,f,u[3]),e.getTag().insertRules(l,o)),o.length=0}else o.push(c)}}},x=function(e){for(var n=document.querySelectorAll(w),o=0,s=n.length;o<s;o++){var a=n[o];a&&a.getAttribute(t)!==r&&(P(e,a),a.parentNode&&a.parentNode.removeChild(a))}};function A(){return"undefined"!=typeof __webpack_nonce__?__webpack_nonce__:null}var E=function(e){var s=document.head,a=e||s,i=document.createElement("style"),c=function(e){var r=Array.from(e.querySelectorAll("style[".concat(t,"]")));return r[r.length-1]}(a),u=void 0!==c?c.nextSibling:null;i.setAttribute(t,r),i.setAttribute(n,o);var l=A();return l&&i.setAttribute("nonce",l),a.insertBefore(i,u),i},_=function(){function e(e){this.element=E(e),this.element.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var t=document.styleSheets,r=0,n=t.length;r<n;r++){var o=t[r];if(o.ownerNode===e)return o}throw d(17)}(this.element),this.length=0}return e.prototype.insertRule=function(e,t){try{return this.sheet.insertRule(t,e),this.length++,!0}catch(e){return!1}},e.prototype.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},e.prototype.getRule=function(e){var t=this.sheet.cssRules[e];return t&&t.cssText?t.cssText:""},e}(),R=function(){function e(e){this.element=E(e),this.nodes=this.element.childNodes,this.length=0}return e.prototype.insertRule=function(e,t){if(e<=this.length&&e>=0){var r=document.createTextNode(t);return this.element.insertBefore(r,this.nodes[e]||null),this.length++,!0}return!1},e.prototype.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},e.prototype.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),$=function(){function e(e){this.rules=[],this.length=0}return e.prototype.insertRule=function(e,t){return e<=this.length&&(this.rules.splice(e,0,t),this.length++,!0)},e.prototype.deleteRule=function(e){this.rules.splice(e,1),this.length--},e.prototype.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),k=a,O={isServer:!a,useCSSOMInjection:!i},N=function(){function e(e,r,n){void 0===e&&(e=p),void 0===r&&(r={});var o=this;this.options=u(u({},O),e),this.gs=r,this.names=new Map(n),this.server=!!e.isServer,!this.server&&a&&k&&(k=!1,x(this)),h(this,function(){return function(e){for(var r=e.getTag(),n=r.length,o="",a=function(n){var a=function(e){return y.get(e)}(n);if(void 0===a)return"continue";var i=e.names.get(a),c=r.getGroup(n);if(void 0===i||!i.size||0===c.length)return"continue";var u="".concat(t,".g").concat(n,'[id="').concat(a,'"]'),l="";void 0!==i&&i.forEach(function(e){e.length>0&&(l+="".concat(e,","))}),o+="".concat(c).concat(u,'{content:"').concat(l,'"}').concat(s)},i=0;i<n;i++)a(i);return o}(o)})}return e.registerId=function(e){return S(e)},e.prototype.rehydrate=function(){!this.server&&a&&x(this)},e.prototype.reconstructWithOptions=function(t,r){return void 0===r&&(r=!0),new e(u(u({},this.options),t),this.gs,r&&this.names||void 0)},e.prototype.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},e.prototype.getTag=function(){return this.tag||(this.tag=(e=function(e){var t=e.useCSSOMInjection,r=e.target;return e.isServer?new $(r):t?new _(r):new R(r)}(this.options),new m(e)));var e},e.prototype.hasNameForId=function(e,t){return this.names.has(e)&&this.names.get(e).has(t)},e.prototype.registerName=function(e,t){if(S(e),this.names.has(e))this.names.get(e).add(t);else{var r=new Set;r.add(t),this.names.set(e,r)}},e.prototype.insertRules=function(e,t,r){this.registerName(e,t),this.getTag().insertRules(S(e),r)},e.prototype.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},e.prototype.clearRules=function(e){this.getTag().clearGroup(S(e)),this.clearNames(e)},e.prototype.clearTag=function(){this.tag=void 0},e}(),j="-ms-",T="-moz-",D="-webkit-",z="comm",F="rule",G="decl",M="@import",B="@keyframes",L="@layer",W=Math.abs,Y=String.fromCharCode,q=Object.assign;function H(e){return e.trim()}function V(e,t){return(e=t.exec(e))?e[0]:e}function U(e,t,r){return e.replace(t,r)}function J(e,t,r){return e.indexOf(t,r)}function Z(e,t){return 0|e.charCodeAt(t)}function K(e,t,r){return e.slice(t,r)}function Q(e){return e.length}function X(e){return e.length}function ee(e,t){return t.push(e),e}function te(e,t){return e.filter(function(e){return!V(e,t)})}var re=1,ne=1,oe=0,se=0,ae=0,ie="";function ce(e,t,r,n,o,s,a,i){return{value:e,root:t,parent:r,type:n,props:o,children:s,line:re,column:ne,length:a,return:"",siblings:i}}function ue(e,t){return q(ce("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},t)}function le(e){for(;e.root;)e=ue(e.root,{children:[e]});ee(e,e.siblings)}function fe(){return ae=se<oe?Z(ie,se++):0,ne++,10===ae&&(ne=1,re++),ae}function pe(){return Z(ie,se)}function he(){return se}function de(e,t){return K(ie,e,t)}function me(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function ve(e){return H(de(se-1,Se(91===e?e+2:40===e?e+1:e)))}function ye(e){for(;(ae=pe())&&ae<33;)fe();return me(e)>2||me(ae)>3?"":" "}function ge(e,t){for(;--t&&fe()&&!(ae<48||ae>102||ae>57&&ae<65||ae>70&&ae<97););return de(e,he()+(t<6&&32==pe()&&32==fe()))}function Se(e){for(;fe();)switch(ae){case e:return se;case 34:case 39:34!==e&&39!==e&&Se(ae);break;case 40:41===e&&Se(e);break;case 92:fe()}return se}function be(e,t){for(;fe()&&e+ae!==57&&(e+ae!==84||47!==pe()););return"/*"+de(t,se-1)+"*"+Y(47===e?e:fe())}function we(e){for(;!me(pe());)fe();return de(e,se)}function Ce(e){return function(e){return ie="",e}(Ie("",null,null,null,[""],e=function(e){return re=ne=1,oe=Q(ie=e),se=0,[]}(e),0,[0],e))}function Ie(e,t,r,n,o,s,a,i,c){for(var u=0,l=0,f=a,p=0,h=0,d=0,m=1,v=1,y=1,g=0,S="",b=o,w=s,C=n,I=S;v;)switch(d=g,g=fe()){case 40:if(108!=d&&58==Z(I,f-1)){-1!=J(I+=U(ve(g),"&","&\f"),"&\f",W(u?i[u-1]:0))&&(y=-1);break}case 34:case 39:case 91:I+=ve(g);break;case 9:case 10:case 13:case 32:I+=ye(d);break;case 92:I+=ge(he()-1,7);continue;case 47:switch(pe()){case 42:case 47:ee(xe(be(fe(),he()),t,r,c),c);break;default:I+="/"}break;case 123*m:i[u++]=Q(I)*y;case 125*m:case 59:case 0:switch(g){case 0:case 125:v=0;case 59+l:-1==y&&(I=U(I,/\f/g,"")),h>0&&Q(I)-f&&ee(h>32?Ae(I+";",n,r,f-1,c):Ae(U(I," ","")+";",n,r,f-2,c),c);break;case 59:I+=";";default:if(ee(C=Pe(I,t,r,u,l,o,i,S,b=[],w=[],f,s),s),123===g)if(0===l)Ie(I,t,C,C,b,s,f,i,w);else switch(99===p&&110===Z(I,3)?100:p){case 100:case 108:case 109:case 115:Ie(e,C,C,n&&ee(Pe(e,C,C,0,0,o,i,S,o,b=[],f,w),w),o,w,f,i,n?b:w);break;default:Ie(I,C,C,C,[""],w,0,i,w)}}u=l=h=0,m=y=1,S=I="",f=a;break;case 58:f=1+Q(I),h=d;default:if(m<1)if(123==g)--m;else if(125==g&&0==m++&&125==(ae=se>0?Z(ie,--se):0,ne--,10===ae&&(ne=1,re--),ae))continue;switch(I+=Y(g),g*m){case 38:y=l>0?1:(I+="\f",-1);break;case 44:i[u++]=(Q(I)-1)*y,y=1;break;case 64:45===pe()&&(I+=ve(fe())),p=pe(),l=f=Q(S=I+=we(he())),g++;break;case 45:45===d&&2==Q(I)&&(m=0)}}return s}function Pe(e,t,r,n,o,s,a,i,c,u,l,f){for(var p=o-1,h=0===o?s:[""],d=X(h),m=0,v=0,y=0;m<n;++m)for(var g=0,S=K(e,p+1,p=W(v=a[m])),b=e;g<d;++g)(b=H(v>0?h[g]+" "+S:U(S,/&\f/g,h[g])))&&(c[y++]=b);return ce(e,t,r,0===o?F:i,c,u,l,f)}function xe(e,t,r,n){return ce(e,t,r,z,Y(ae),K(e,2,-2),0,n)}function Ae(e,t,r,n,o){return ce(e,t,r,G,K(e,0,n),K(e,n+1,-1),n,o)}function Ee(e,t,r){switch(function(e,t){return 45^Z(e,0)?(((t<<2^Z(e,0))<<2^Z(e,1))<<2^Z(e,2))<<2^Z(e,3):0}(e,t)){case 5103:return D+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return D+e+e;case 4789:return T+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return D+e+T+e+j+e+e;case 5936:switch(Z(e,t+11)){case 114:return D+e+j+U(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return D+e+j+U(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return D+e+j+U(e,/[svh]\w+-[tblr]{2}/,"lr")+e}case 6828:case 4268:case 2903:return D+e+j+e+e;case 6165:return D+e+j+"flex-"+e+e;case 5187:return D+e+U(e,/(\w+).+(:[^]+)/,D+"box-$1$2"+j+"flex-$1$2")+e;case 5443:return D+e+j+"flex-item-"+U(e,/flex-|-self/g,"")+(V(e,/flex-|baseline/)?"":j+"grid-row-"+U(e,/flex-|-self/g,""))+e;case 4675:return D+e+j+"flex-line-pack"+U(e,/align-content|flex-|-self/g,"")+e;case 5548:return D+e+j+U(e,"shrink","negative")+e;case 5292:return D+e+j+U(e,"basis","preferred-size")+e;case 6060:return D+"box-"+U(e,"-grow","")+D+e+j+U(e,"grow","positive")+e;case 4554:return D+U(e,/([^-])(transform)/g,"$1"+D+"$2")+e;case 6187:return U(U(U(e,/(zoom-|grab)/,D+"$1"),/(image-set)/,D+"$1"),e,"")+e;case 5495:case 3959:return U(e,/(image-set\([^]*)/,D+"$1$`$1");case 4968:return U(U(e,/(.+:)(flex-)?(.*)/,D+"box-pack:$3"+j+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+D+e+e;case 4200:if(!V(e,/flex-|baseline/))return j+"grid-column-align"+K(e,t)+e;break;case 2592:case 3360:return j+U(e,"template-","")+e;case 4384:case 3616:return r&&r.some(function(e,r){return t=r,V(e.props,/grid-\w+-end/)})?~J(e+(r=r[t].value),"span",0)?e:j+U(e,"-start","")+e+j+"grid-row-span:"+(~J(r,"span",0)?V(r,/\d+/):+V(r,/\d+/)-+V(e,/\d+/))+";":j+U(e,"-start","")+e;case 4896:case 4128:return r&&r.some(function(e){return V(e.props,/grid-\w+-start/)})?e:j+U(U(e,"-end","-span"),"span ","")+e;case 4095:case 3583:case 4068:case 2532:return U(e,/(.+)-inline(.+)/,D+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(Q(e)-1-t>6)switch(Z(e,t+1)){case 109:if(45!==Z(e,t+4))break;case 102:return U(e,/(.+:)(.+)-([^]+)/,"$1"+D+"$2-$3$1"+T+(108==Z(e,t+3)?"$3":"$2-$3"))+e;case 115:return~J(e,"stretch",0)?Ee(U(e,"stretch","fill-available"),t,r)+e:e}break;case 5152:case 5920:return U(e,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,function(t,r,n,o,s,a,i){return j+r+":"+n+i+(o?j+r+"-span:"+(s?a:+a-+n)+i:"")+e});case 4949:if(121===Z(e,t+6))return U(e,":",":"+D)+e;break;case 6444:switch(Z(e,45===Z(e,14)?18:11)){case 120:return U(e,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+D+(45===Z(e,14)?"inline-":"")+"box$3$1"+D+"$2$3$1"+j+"$2box$3")+e;case 100:return U(e,":",":"+j)+e}break;case 5719:case 2647:case 2135:case 3927:case 2391:return U(e,"scroll-","scroll-snap-")+e}return e}function _e(e,t){for(var r="",n=0;n<e.length;n++)r+=t(e[n],n,e,t)||"";return r}function Re(e,t,r,n){switch(e.type){case L:if(e.children.length)break;case M:case G:return e.return=e.return||e.value;case z:return"";case B:return e.return=e.value+"{"+_e(e.children,n)+"}";case F:if(!Q(e.value=e.props.join(",")))return""}return Q(r=_e(e.children,n))?e.return=e.value+"{"+r+"}":""}function $e(e,t,r,n){if(e.length>-1&&!e.return)switch(e.type){case G:return void(e.return=Ee(e.value,e.length,r));case B:return _e([ue(e,{value:U(e.value,"@","@"+D)})],n);case F:if(e.length)return function(e,t){return e.map(t).join("")}(r=e.props,function(t){switch(V(t,n=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":le(ue(e,{props:[U(t,/:(read-\w+)/,":"+T+"$1")]})),le(ue(e,{props:[t]})),q(e,{props:te(r,n)});break;case"::placeholder":le(ue(e,{props:[U(t,/:(plac\w+)/,":"+D+"input-$1")]})),le(ue(e,{props:[U(t,/:(plac\w+)/,":"+T+"$1")]})),le(ue(e,{props:[U(t,/:(plac\w+)/,j+"input-$1")]})),le(ue(e,{props:[t]})),q(e,{props:te(r,n)})}return""})}}var ke=5381,Oe=function(e,t){for(var r=t.length;r;)e=33*e^t.charCodeAt(--r);return e},Ne=function(e){return Oe(ke,e)},je=/&/g,Te=/^\s*\/\/.*$/gm;function De(e,t){return e.map(function(e){return"rule"===e.type&&(e.value="".concat(t," ").concat(e.value),e.value=e.value.replaceAll(",",",".concat(t," ")),e.props=e.props.map(function(e){return"".concat(t," ").concat(e)})),Array.isArray(e.children)&&"@keyframes"!==e.type&&(e.children=De(e.children,t)),e})}function ze(e){var t,r,n,o=void 0===e?p:e,s=o.options,a=void 0===s?p:s,i=o.plugins,c=void 0===i?f:i,u=function(e,n,o){return o.startsWith(r)&&o.endsWith(r)&&o.replaceAll(r,"").length>0?".".concat(t):e},l=c.slice();l.push(function(e){e.type===F&&e.value.includes("&")&&(e.props[0]=e.props[0].replace(je,r).replace(n,u))}),a.prefix&&l.push($e),l.push(Re);var h=function(e,o,s,i){void 0===o&&(o=""),void 0===s&&(s=""),void 0===i&&(i="&"),t=i,r=o,n=new RegExp("\\".concat(r,"\\b"),"g");var c=e.replace(Te,""),u=Ce(s||o?"".concat(s," ").concat(o," { ").concat(c," }"):c);a.namespace&&(u=De(u,a.namespace));var f=[];return _e(u,function(e){var t=X(e);return function(r,n,o,s){for(var a="",i=0;i<t;i++)a+=e[i](r,n,o,s)||"";return a}}(l.concat(function(e){e.root||(e=e.return)&&f.push(e)}))),f};return h.hash=c.length?c.reduce(function(e,t){return t.name||d(15),Oe(e,t.name)},ke).toString():"",h}var Fe=new N,Ge=ze(),Me=e.createContext({shouldForwardProp:void 0,styleSheet:Fe,stylis:Ge}),Be=Me.Consumer,Le=e.createContext(void 0);function We(){return e.useContext(Me)}function Ye(t){var r=e.useState(t.stylisPlugins),n=r[0],o=r[1],s=We().styleSheet,a=e.useMemo(function(){var e=s;return t.sheet?e=t.sheet:t.target&&(e=e.reconstructWithOptions({target:t.target},!1)),t.disableCSSOMInjection&&(e=e.reconstructWithOptions({useCSSOMInjection:!1})),e},[t.disableCSSOMInjection,t.sheet,t.target,s]),i=e.useMemo(function(){return ze({options:{namespace:t.namespace,prefix:t.enableVendorPrefixes},plugins:n})},[t.enableVendorPrefixes,t.namespace,n]);e.useEffect(function(){(function(e,t,r,n){var o=void 0;if(void 0!==o)return!!o;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;var s=Object.keys(e),a=Object.keys(t);if(s.length!==a.length)return!1;for(var i=Object.prototype.hasOwnProperty.bind(t),c=0;c<s.length;c++){var u=s[c];if(!i(u))return!1;if(!1===(o=void 0)||void 0===o&&e[u]!==t[u])return!1}return!0})(n,t.stylisPlugins)||o(t.stylisPlugins)},[t.stylisPlugins]);var c=e.useMemo(function(){return{shouldForwardProp:t.shouldForwardProp,styleSheet:a,stylis:i}},[t.shouldForwardProp,a,i]);return e.createElement(Me.Provider,{value:c},e.createElement(Le.Provider,{value:i},t.children))}var qe=function(){function e(e,t){var r=this;this.inject=function(e,t){void 0===t&&(t=Ge);var n=r.name+t.hash;e.hasNameForId(r.id,n)||e.insertRules(r.id,n,t(r.rules,n,"@keyframes"))},this.name=e,this.id="sc-keyframes-".concat(e),this.rules=t,h(this,function(){throw d(12,String(r.name))})}return e.prototype.getName=function(e){return void 0===e&&(e=Ge),this.name+e.hash},e}(),He={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};function Ve(e){return e.displayName||e.name||"Component"}var Ue=function(e){return e>="A"&&e<="Z"};function Je(e){for(var t="",r=0;r<e.length;r++){var n=e[r];if(1===r&&"-"===n&&"-"===e[0])return e;Ue(n)?t+="-"+n.toLowerCase():t+=n}return t.startsWith("ms-")?"-"+t:t}function Ze(e){return"function"==typeof e}function Ke(e){return null!==e&&"object"==typeof e&&e.constructor.name===Object.name&&!("props"in e&&e.$$typeof)}function Qe(e){return"object"==typeof e&&"styledComponentId"in e}var Xe=function(e){return null==e||!1===e||""===e},et=function(e){var t,r,n=[];for(var o in e){var s=e[o];e.hasOwnProperty(o)&&!Xe(s)&&(Array.isArray(s)&&s.isCss||Ze(s)?n.push("".concat(Je(o),":"),s,";"):Ke(s)?n.push.apply(n,l(l(["".concat(o," {")],et(s),!1),["}"],!1)):n.push("".concat(Je(o),": ").concat((t=o,null==(r=s)||"boolean"==typeof r||""===r?"":"number"!=typeof r||0===r||t in He||t.startsWith("--")?String(r).trim():"".concat(r,"px")),";")))}return n};function tt(e,t,r,n){return Xe(e)?[]:Qe(e)?[".".concat(e.styledComponentId)]:Ze(e)?!Ze(o=e)||o.prototype&&o.prototype.isReactComponent||!t?[e]:tt(e(t),t,r,n):e instanceof qe?r?(e.inject(r,n),[e.getName(n)]):[e]:Ke(e)?et(e):Array.isArray(e)?Array.prototype.concat.apply(f,e.map(function(e){return tt(e,t,r,n)})):[e.toString()];var o}function rt(e){for(var t=0;t<e.length;t+=1){var r=e[t];if(Ze(r)&&!Qe(r))return!1}return!0}function nt(e,t){return e&&t?"".concat(e," ").concat(t):e||t||""}function ot(e,t){if(0===e.length)return"";for(var r=e[0],n=1;n<e.length;n++)r+=t?t+e[n]:e[n];return r}var st=function(){function e(e,t){this.rules=e,this.componentId=t,this.isStatic=rt(e),N.registerId(this.componentId+1)}return e.prototype.createStyles=function(e,t,r,n){var o=n(ot(tt(this.rules,t,r,n)),""),s=this.componentId+e;r.insertRules(s,s,o)},e.prototype.removeStyles=function(e,t){t.clearRules(this.componentId+e)},e.prototype.renderStyles=function(e,t,r,n){e>2&&N.registerId(this.componentId+e),this.removeStyles(e,r),this.createStyles(e,t,r,n)},e}(),at=e.createContext(void 0),it=at.Consumer;function ct(e,t,r){return void 0===r&&(r=p),e.theme!==r.theme&&e.theme||t||r.theme}var ut=/(a)(d)/gi,lt=52,ft=function(e){return String.fromCharCode(e+(e>25?39:97))};function pt(e){var t,r="";for(t=Math.abs(e);t>lt;t=t/lt|0)r=ft(t%lt)+r;return(ft(t%lt)+r).replace(ut,"$1-$2")}function ht(e){return pt(Ne(e)>>>0)}function dt(e,t){for(var r=[e[0]],n=0,o=t.length;n<o;n+=1)r.push(t[n],e[n+1]);return r}var mt,vt=function(e){return Object.assign(e,{isCss:!0})};function yt(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(Ze(e)||Ke(e))return vt(tt(dt(f,l([e],t,!0))));var n=e;return 0===t.length&&1===n.length&&"string"==typeof n[0]?tt(n):vt(tt(dt(n,t)))}var gt="function"==typeof Symbol&&Symbol.for,St=gt?Symbol.for("react.memo"):60115,bt=gt?Symbol.for("react.forward_ref"):60112,wt={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},Ct={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},It={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Pt=((mt={})[bt]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},mt[St]=It,mt);function xt(e){return("type"in(t=e)&&t.type.$$typeof)===St?It:"$$typeof"in e?Pt[e.$$typeof]:wt;var t}var At=Object.defineProperty,Et=Object.getOwnPropertyNames,_t=Object.getOwnPropertySymbols,Rt=Object.getOwnPropertyDescriptor,$t=Object.getPrototypeOf,kt=Object.prototype;function Ot(e,t,r){if("string"!=typeof t){if(kt){var n=$t(t);n&&n!==kt&&Ot(e,n,r)}var o=Et(t);_t&&(o=o.concat(_t(t)));for(var s=xt(e),a=xt(t),i=0;i<o.length;++i){var c=o[i];if(!(c in Ct||r&&r[c]||a&&c in a||s&&c in s)){var u=Rt(t,c);try{At(e,c,u)}catch(e){}}}}return e}var Nt=function(){function r(){var r=this;this._emitSheetCSS=function(){var e=r.instance.toString();if(!e)return"";var s=A(),a=ot([s&&'nonce="'.concat(s,'"'),"".concat(t,'="true"'),"".concat(n,'="').concat(o,'"')].filter(Boolean)," ");return"<style ".concat(a,">").concat(e,"</style>")},this.getStyleTags=function(){if(r.sealed)throw d(2);return r._emitSheetCSS()},this.getStyleElement=function(){var s;if(r.sealed)throw d(2);var a=r.instance.toString();if(!a)return[];var i=((s={})[t]="",s[n]=o,s.dangerouslySetInnerHTML={__html:a},s),c=A();return c&&(i.nonce=c),[e.createElement("style",u({},i,{key:"sc-0-0"}))]},this.seal=function(){r.sealed=!0},this.instance=new N({isServer:!0}),this.sealed=!1}return r.prototype.collectStyles=function(t){if(this.sealed)throw d(2);return e.createElement(Ye,{sheet:this.instance},t)},r.prototype.interleaveWithNodeStream=function(e){throw d(3)},r}(),jt=/*#__PURE__*/Object.freeze({__proto__:null,ServerStyleSheet:Nt,StyleSheetConsumer:Be,StyleSheetContext:Me,StyleSheetManager:Ye,ThemeConsumer:it,ThemeContext:at,ThemeProvider:function(t){var r=e.useContext(at),n=e.useMemo(function(){return function(e,t){if(!e)throw d(14);if(Ze(e))return e(t);if(Array.isArray(e)||"object"!=typeof e)throw d(8);return t?u(u({},t),e):e}(t.theme,r)},[t.theme,r]);return t.children?e.createElement(at.Provider,{value:n},t.children):null},__PRIVATE__:{StyleSheet:N,mainSheet:Fe},createGlobalStyle:function(t){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var o=yt.apply(void 0,l([t],r,!1)),s="sc-global-".concat(ht(JSON.stringify(o))),a=new st(o,s),i=function(t){var r=We(),n=e.useContext(at),o=e.useRef(r.styleSheet.allocateGSInstance(s)).current;return r.styleSheet.server&&f(o,t,r.styleSheet,n,r.stylis),e.useLayoutEffect(function(){if(!r.styleSheet.server)return f(o,t,r.styleSheet,n,r.stylis),function(){return a.removeStyles(o,r.styleSheet)}},[o,t,r.styleSheet,n,r.stylis]),null};function f(e,t,r,n,o){if(a.isStatic)a.renderStyles(e,c,r,o);else{var s=u(u({},t),{theme:ct(t,n,i.defaultProps)});a.renderStyles(e,s,r,o)}}return e.memo(i)},css:yt,isStyledComponent:Qe,keyframes:function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];var n=ot(yt.apply(void 0,l([e],t,!1))),o=ht(n);return new qe(o,n)},useTheme:function(){var t=e.useContext(at);if(!t)throw d(18);return t},version:o,withTheme:function(t){var r=e.forwardRef(function(r,n){var o=ct(r,e.useContext(at),t.defaultProps);return e.createElement(t,u({},r,{theme:o,ref:n}))});return r.displayName="WithTheme(".concat(Ve(t),")"),Ot(r,t)}}),Tt=new Set(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","use","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"]),Dt=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,zt=/(^-|-$)/g;function Ft(e){return e.replace(Dt,"-").replace(zt,"")}function Gt(e){return"string"==typeof e&&!0}function Mt(e,t,r){if(void 0===r&&(r=!1),!r&&!Ke(e)&&!Array.isArray(e))return t;if(Array.isArray(t))for(var n=0;n<t.length;n++)e[n]=Mt(e[n],t[n]);else if(Ke(t))for(var n in t)e[n]=Mt(e[n],t[n]);return e}var Bt=Ne(o),Lt=function(){function e(e,t,r){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===r||r.isStatic)&&rt(e),this.componentId=t,this.baseHash=Oe(Bt,t),this.baseStyle=r,N.registerId(t)}return e.prototype.generateAndInjectStyles=function(e,t,r){var n=this.baseStyle?this.baseStyle.generateAndInjectStyles(e,t,r):"";if(this.isStatic&&!r.hash)if(this.staticRulesId&&t.hasNameForId(this.componentId,this.staticRulesId))n=nt(n,this.staticRulesId);else{var o=ot(tt(this.rules,e,t,r)),s=pt(Oe(this.baseHash,o)>>>0);if(!t.hasNameForId(this.componentId,s)){var a=r(o,".".concat(s),void 0,this.componentId);t.insertRules(this.componentId,s,a)}n=nt(n,s),this.staticRulesId=s}else{for(var i=Oe(this.baseHash,r.hash),c="",u=0;u<this.rules.length;u++){var l=this.rules[u];if("string"==typeof l)c+=l;else if(l){var f=ot(tt(l,e,t,r));i=Oe(i,f+u),c+=f}}if(c){var p=pt(i>>>0);t.hasNameForId(this.componentId,p)||t.insertRules(this.componentId,p,r(c,".".concat(p),void 0,this.componentId)),n=nt(n,p)}}return n},e}(),Wt={};function Yt(t,r,n){var s=Qe(t),a=t,i=!Gt(t),c=r.attrs,l=void 0===c?f:c,d=r.componentId,m=void 0===d?function(e,t){var r="string"!=typeof e?"sc":Ft(e);Wt[r]=(Wt[r]||0)+1;var n="".concat(r,"-").concat(ht(o+r+Wt[r]));return t?"".concat(t,"-").concat(n):n}(r.displayName,r.parentComponentId):d,v=r.displayName,y=void 0===v?function(e){return Gt(e)?"styled.".concat(e):"Styled(".concat(Ve(e),")")}(t):v,g=r.displayName&&r.componentId?"".concat(Ft(r.displayName),"-").concat(r.componentId):r.componentId||m,S=s&&a.attrs?a.attrs.concat(l).filter(Boolean):l,b=r.shouldForwardProp;if(s&&a.shouldForwardProp){var w=a.shouldForwardProp;if(r.shouldForwardProp){var C=r.shouldForwardProp;b=function(e,t){return w(e,t)&&C(e,t)}}else b=w}var I=new Lt(n,g,s?a.componentStyle:void 0);function P(t,r){return function(t,r,n){var o=t.attrs,s=t.componentStyle,a=t.defaultProps,i=t.foldedComponentIds,c=t.styledComponentId,l=t.target,f=e.useContext(at),h=We(),d=t.shouldForwardProp||h.shouldForwardProp,m=ct(r,f,a)||p,v=function(e,t,r){for(var n,o=u(u({},t),{className:void 0,theme:r}),s=0;s<e.length;s+=1){var a=Ze(n=e[s])?n(o):n;for(var i in a)o[i]="className"===i?nt(o[i],a[i]):"style"===i?u(u({},o[i]),a[i]):a[i]}return t.className&&(o.className=nt(o.className,t.className)),o}(o,r,m),y=v.as||l,g={};for(var S in v)void 0===v[S]||"$"===S[0]||"as"===S||"theme"===S&&v.theme===m||("forwardedAs"===S?g.as=v.forwardedAs:d&&!d(S,y)||(g[S]=v[S]));var b=function(e,t){var r=We();return e.generateAndInjectStyles(t,r.styleSheet,r.stylis)}(s,v),w=nt(i,c);return b&&(w+=" "+b),v.className&&(w+=" "+v.className),g[Gt(y)&&!Tt.has(y)?"class":"className"]=w,n&&(g.ref=n),e.createElement(y,g)}(x,t,r)}P.displayName=y;var x=e.forwardRef(P);return x.attrs=S,x.componentStyle=I,x.displayName=y,x.shouldForwardProp=b,x.foldedComponentIds=s?nt(a.foldedComponentIds,a.styledComponentId):"",x.styledComponentId=g,x.target=s?a.target:t,Object.defineProperty(x,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(e){this._foldedDefaultProps=s?function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];for(var n=0,o=t;n<o.length;n++)Mt(e,o[n],!0);return e}({},a.defaultProps,e):e}}),h(x,function(){return".".concat(x.styledComponentId)}),i&&Ot(x,t,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0}),x}function qt(e,t,r){if(void 0===r&&(r=p),!t)throw d(1,t);var n=function(n){for(var o=[],s=1;s<arguments.length;s++)o[s-1]=arguments[s];return e(t,r,yt.apply(void 0,l([n],o,!1)))};return n.attrs=function(n){return qt(e,t,u(u({},r),{attrs:Array.prototype.concat(r.attrs,n).filter(Boolean)}))},n.withConfig=function(n){return qt(e,t,u(u({},r),n))},n}new Set;var Ht=function(e){return qt(Yt,e)},Vt=Ht;for(var Ut in Tt.forEach(function(e){Vt[e]=Ht(e)}),jt)Vt[Ut]=jt[Ut];return Vt});
//# sourceMappingURL=styled-components.min.js.map
